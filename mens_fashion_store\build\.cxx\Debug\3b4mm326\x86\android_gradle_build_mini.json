{"buildFiles": ["C:\\Users\\<USER>\\dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\flutter_application_1\\mens_fashion_store\\build\\.cxx\\Debug\\3b4mm326\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\flutter_application_1\\mens_fashion_store\\build\\.cxx\\Debug\\3b4mm326\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}