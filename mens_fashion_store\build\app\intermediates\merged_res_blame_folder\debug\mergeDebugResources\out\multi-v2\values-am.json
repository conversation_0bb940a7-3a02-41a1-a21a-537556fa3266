{"logs": [{"outputFile": "com.mensfashion.store.mens_fashion_store.app-mergeDebugResources-40:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0d0ad2c9a7eee0ad2b557032bddebd70\\transformed\\appcompat-1.1.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,868,959,1051,1143,1237,1338,1431,1526,1619,1710,1801,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,76,90,91,91,93,100,92,94,92,90,90,78,99,99,95,101,99,98,149,95,78", "endOffsets": "198,296,402,488,591,708,786,863,954,1046,1138,1232,1333,1426,1521,1614,1705,1796,1875,1975,2075,2171,2273,2373,2472,2622,2718,2797"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,44", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,868,959,1051,1143,1237,1338,1431,1526,1619,1710,1801,1880,1980,2080,2176,2278,2378,2477,2627,4157", "endColumns": "97,97,105,85,102,116,77,76,90,91,91,93,100,92,94,92,90,90,78,99,99,95,101,99,98,149,95,78", "endOffsets": "198,296,402,488,591,708,786,863,954,1046,1138,1232,1333,1426,1521,1614,1705,1796,1875,1975,2075,2171,2273,2373,2472,2622,2718,4231"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,253,327,458,627,708", "endColumns": "65,81,73,130,168,80,77", "endOffsets": "166,248,322,453,622,703,781"}, "to": {"startLines": "36,38,42,43,46,47,48", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3410,3572,3952,4026,4337,4506,4587", "endColumns": "65,81,73,130,168,80,77", "endOffsets": "3471,3649,4021,4152,4501,4582,4660"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "29,30,31,32,33,34,35,45", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2723,2816,2916,3013,3112,3208,3310,4236", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "2811,2911,3008,3107,3203,3305,3405,4332"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,246,352", "endColumns": "95,94,105,96", "endOffsets": "146,241,347,444"}, "to": {"startLines": "37,39,40,41", "startColumns": "4,4,4,4", "startOffsets": "3476,3654,3749,3855", "endColumns": "95,94,105,96", "endOffsets": "3567,3744,3850,3947"}}]}]}