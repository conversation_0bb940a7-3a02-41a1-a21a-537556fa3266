import 'dart:convert';
import 'package:crypto/crypto.dart';

/// نموذج المستخدم الإداري
/// يحتوي على جميع المعلومات والأذونات الخاصة بالمدير
class AdminUser {
  final String id;
  final String username;
  final String email;
  final String passwordHash;
  final String fullName;
  final AdminRole role;
  final List<AdminPermission> permissions;
  final DateTime createdAt;
  final DateTime lastLoginAt;
  final bool isActive;
  final String? profileImage;
  final Map<String, dynamic> settings;

  const AdminUser({
    required this.id,
    required this.username,
    required this.email,
    required this.passwordHash,
    required this.fullName,
    required this.role,
    required this.permissions,
    required this.createdAt,
    required this.lastLoginAt,
    required this.isActive,
    this.profileImage,
    this.settings = const {},
  });

  /// إنشاء مستخدم إداري جديد
  factory AdminUser.create({
    required String username,
    required String email,
    required String password,
    required String fullName,
    required AdminRole role,
    List<AdminPermission> permissions = const [],
  }) {
    return AdminUser(
      id: _generateId(),
      username: username,
      email: email,
      passwordHash: _hashPassword(password),
      fullName: fullName,
      role: role,
      permissions: permissions,
      createdAt: DateTime.now(),
      lastLoginAt: DateTime.now(),
      isActive: true,
    );
  }

  /// إنشاء المدير الأساسي (Super Admin)
  factory AdminUser.createSuperAdmin({
    required String username,
    required String email,
    required String password,
    required String fullName,
  }) {
    return AdminUser(
      id: 'super_admin_001',
      username: username,
      email: email,
      passwordHash: _hashPassword(password),
      fullName: fullName,
      role: AdminRole.superAdmin,
      permissions: AdminPermission.getAllPermissions(),
      createdAt: DateTime.now(),
      lastLoginAt: DateTime.now(),
      isActive: true,
    );
  }

  /// التحقق من كلمة المرور
  bool verifyPassword(String password) {
    return passwordHash == _hashPassword(password);
  }

  /// التحقق من وجود صلاحية معينة
  bool hasPermission(AdminPermission permission) {
    return permissions.contains(permission) || role == AdminRole.superAdmin;
  }

  /// التحقق من إمكانية الوصول لصفحة معينة
  bool canAccessPage(String pageName) {
    switch (pageName) {
      case 'products':
        return hasPermission(AdminPermission.manageProducts);
      case 'orders':
        return hasPermission(AdminPermission.manageOrders);
      case 'users':
        return hasPermission(AdminPermission.manageUsers);
      case 'analytics':
        return hasPermission(AdminPermission.viewAnalytics);
      case 'settings':
        return hasPermission(AdminPermission.manageSettings);
      default:
        return false;
    }
  }

  /// نسخ المستخدم مع تحديث بعض الحقول
  AdminUser copyWith({
    String? username,
    String? email,
    String? fullName,
    AdminRole? role,
    List<AdminPermission>? permissions,
    DateTime? lastLoginAt,
    bool? isActive,
    String? profileImage,
    Map<String, dynamic>? settings,
  }) {
    return AdminUser(
      id: id,
      username: username ?? this.username,
      email: email ?? this.email,
      passwordHash: passwordHash,
      fullName: fullName ?? this.fullName,
      role: role ?? this.role,
      permissions: permissions ?? this.permissions,
      createdAt: createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      isActive: isActive ?? this.isActive,
      profileImage: profileImage ?? this.profileImage,
      settings: settings ?? this.settings,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'passwordHash': passwordHash,
      'fullName': fullName,
      'role': role.name,
      'permissions': permissions.map((p) => p.name).toList(),
      'createdAt': createdAt.toIso8601String(),
      'lastLoginAt': lastLoginAt.toIso8601String(),
      'isActive': isActive,
      'profileImage': profileImage,
      'settings': settings,
    };
  }

  /// إنشاء من JSON
  factory AdminUser.fromJson(Map<String, dynamic> json) {
    return AdminUser(
      id: json['id'],
      username: json['username'],
      email: json['email'],
      passwordHash: json['passwordHash'],
      fullName: json['fullName'],
      role: AdminRole.values.firstWhere((r) => r.name == json['role']),
      permissions: (json['permissions'] as List)
          .map(
            (p) => AdminPermission.values.firstWhere((perm) => perm.name == p),
          )
          .toList(),
      createdAt: DateTime.parse(json['createdAt']),
      lastLoginAt: DateTime.parse(json['lastLoginAt']),
      isActive: json['isActive'],
      profileImage: json['profileImage'],
      settings: json['settings'] ?? {},
    );
  }

  /// تشفير كلمة المرور
  static String _hashPassword(String password) {
    final bytes = utf8.encode('$password vip_store_salt');
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// توليد معرف فريد
  static String _generateId() {
    return 'admin_${DateTime.now().millisecondsSinceEpoch}';
  }

  @override
  String toString() {
    return 'AdminUser(id: $id, username: $username, role: ${role.name})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AdminUser && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// أدوار المدراء
enum AdminRole {
  superAdmin('مدير عام', 'Super Admin'),
  admin('مدير', 'Admin'),
  moderator('مشرف', 'Moderator'),
  viewer('مشاهد', 'Viewer');

  const AdminRole(this.arabicName, this.englishName);

  final String arabicName;
  final String englishName;

  String getLocalizedName(bool isArabic) {
    return isArabic ? arabicName : englishName;
  }
}

/// صلاحيات المدراء
enum AdminPermission {
  // إدارة المنتجات
  manageProducts('إدارة المنتجات', 'Manage Products'),
  addProducts('إضافة منتجات', 'Add Products'),
  editProducts('تعديل منتجات', 'Edit Products'),
  deleteProducts('حذف منتجات', 'Delete Products'),

  // إدارة الطلبات
  manageOrders('إدارة الطلبات', 'Manage Orders'),
  viewOrders('عرض الطلبات', 'View Orders'),
  updateOrderStatus('تحديث حالة الطلب', 'Update Order Status'),

  // إدارة المستخدمين
  manageUsers('إدارة المستخدمين', 'Manage Users'),
  viewUsers('عرض المستخدمين', 'View Users'),
  banUsers('حظر المستخدمين', 'Ban Users'),

  // الإحصائيات والتقارير
  viewAnalytics('عرض الإحصائيات', 'View Analytics'),
  exportReports('تصدير التقارير', 'Export Reports'),

  // إدارة الإعدادات
  manageSettings('إدارة الإعدادات', 'Manage Settings'),
  manageCategories('إدارة الأقسام', 'Manage Categories'),

  // إدارة المحتوى
  manageContent('إدارة المحتوى', 'Manage Content'),
  manageBanners('إدارة البانرات', 'Manage Banners');

  const AdminPermission(this.arabicName, this.englishName);

  final String arabicName;
  final String englishName;

  String getLocalizedName(bool isArabic) {
    return isArabic ? arabicName : englishName;
  }

  /// الحصول على جميع الصلاحيات
  static List<AdminPermission> getAllPermissions() {
    return AdminPermission.values;
  }

  /// الحصول على صلاحيات المدير العادي
  static List<AdminPermission> getAdminPermissions() {
    return [
      manageProducts,
      addProducts,
      editProducts,
      manageOrders,
      viewOrders,
      updateOrderStatus,
      viewUsers,
      viewAnalytics,
      manageCategories,
      manageContent,
    ];
  }

  /// الحصول على صلاحيات المشرف
  static List<AdminPermission> getModeratorPermissions() {
    return [
      viewOrders,
      updateOrderStatus,
      viewUsers,
      viewAnalytics,
      manageContent,
    ];
  }

  /// الحصول على صلاحيات المشاهد
  static List<AdminPermission> getViewerPermissions() {
    return [viewOrders, viewUsers, viewAnalytics];
  }
}
