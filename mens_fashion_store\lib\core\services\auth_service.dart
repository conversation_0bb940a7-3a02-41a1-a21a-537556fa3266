import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

import '../../shared/models/admin_user.dart';
import 'database_service.dart';

/// خدمة المصادقة والأمان
/// تدير جميع عمليات تسجيل الدخول والخروج والأذونات
class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final DatabaseService _databaseService = DatabaseService();
  AdminUser? _currentAdmin;
  Timer? _sessionTimer;

  // مدة انتهاء الجلسة (30 دقيقة)
  static const Duration _sessionTimeout = Duration(minutes: 30);

  /// الحصول على المدير الحالي
  AdminUser? get currentAdmin => _currentAdmin;

  /// التحقق من تسجيل الدخول
  bool get isLoggedIn => _currentAdmin != null;

  /// التحقق من كون المستخدم مدير عام
  bool get isSuperAdmin => _currentAdmin?.role == AdminRole.superAdmin;

  /// تسجيل الدخول
  Future<AuthResult> login(String username, String password) async {
    try {
      // البحث عن المدير في قاعدة البيانات
      final admin = await _databaseService.getAdminByUsername(username);
      
      if (admin == null) {
        return AuthResult.failure('اسم المستخدم غير موجود');
      }

      if (!admin.isActive) {
        return AuthResult.failure('الحساب معطل. تواصل مع المدير');
      }

      // التحقق من كلمة المرور
      if (!admin.verifyPassword(password)) {
        await _logFailedLogin(username, 'كلمة مرور خاطئة');
        return AuthResult.failure('كلمة المرور غير صحيحة');
      }

      // تسجيل الدخول بنجاح
      _currentAdmin = admin;
      await _saveSession(admin);
      await _databaseService.updateAdminLastLogin(admin.id);
      await _databaseService.addAuditLog(
        adminId: admin.id,
        action: 'تسجيل دخول',
        targetType: 'auth',
        details: 'تم تسجيل الدخول بنجاح',
      );

      _startSessionTimer();
      
      return AuthResult.success(admin);
    } catch (e) {
      return AuthResult.failure('خطأ في تسجيل الدخول: $e');
    }
  }

  /// تسجيل الخروج
  Future<void> logout() async {
    if (_currentAdmin != null) {
      await _databaseService.addAuditLog(
        adminId: _currentAdmin!.id,
        action: 'تسجيل خروج',
        targetType: 'auth',
        details: 'تم تسجيل الخروج',
      );
    }

    _currentAdmin = null;
    _sessionTimer?.cancel();
    await _clearSession();
  }

  /// التحقق من الجلسة المحفوظة
  Future<bool> checkSavedSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionData = prefs.getString('admin_session');
      
      if (sessionData == null) return false;

      final session = jsonDecode(sessionData);
      final sessionTime = DateTime.parse(session['timestamp']);
      
      // التحقق من انتهاء الجلسة
      if (DateTime.now().difference(sessionTime) > _sessionTimeout) {
        await _clearSession();
        return false;
      }

      // استرجاع بيانات المدير
      final admin = await _databaseService.getAdminByUsername(session['username']);
      if (admin != null && admin.isActive) {
        _currentAdmin = admin;
        _startSessionTimer();
        return true;
      }

      return false;
    } catch (e) {
      await _clearSession();
      return false;
    }
  }

  /// التحقق من الصلاحية
  bool hasPermission(AdminPermission permission) {
    return _currentAdmin?.hasPermission(permission) ?? false;
  }

  /// التحقق من إمكانية الوصول لصفحة
  bool canAccessPage(String pageName) {
    return _currentAdmin?.canAccessPage(pageName) ?? false;
  }

  /// تغيير كلمة المرور
  Future<AuthResult> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    if (_currentAdmin == null) {
      return AuthResult.failure('يجب تسجيل الدخول أولاً');
    }

    if (!_currentAdmin!.verifyPassword(currentPassword)) {
      return AuthResult.failure('كلمة المرور الحالية غير صحيحة');
    }

    if (newPassword.length < 8) {
      return AuthResult.failure('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
    }

    try {
      // هنا يمكن إضافة منطق تحديث كلمة المرور في قاعدة البيانات
      await _databaseService.addAuditLog(
        adminId: _currentAdmin!.id,
        action: 'تغيير كلمة المرور',
        targetType: 'auth',
        details: 'تم تغيير كلمة المرور بنجاح',
      );

      return AuthResult.success(_currentAdmin!);
    } catch (e) {
      return AuthResult.failure('خطأ في تغيير كلمة المرور: $e');
    }
  }

  /// إنشاء مدير جديد (للمدير العام فقط)
  Future<AuthResult> createAdmin({
    required String username,
    required String email,
    required String password,
    required String fullName,
    required AdminRole role,
    List<AdminPermission> permissions = const [],
  }) async {
    if (!isSuperAdmin) {
      return AuthResult.failure('ليس لديك صلاحية لإنشاء مدراء جدد');
    }

    try {
      // التحقق من عدم وجود اسم المستخدم
      final existingAdmin = await _databaseService.getAdminByUsername(username);
      if (existingAdmin != null) {
        return AuthResult.failure('اسم المستخدم موجود بالفعل');
      }

      // التحقق من عدم وجود البريد الإلكتروني
      final existingEmail = await _databaseService.getAdminByEmail(email);
      if (existingEmail != null) {
        return AuthResult.failure('البريد الإلكتروني موجود بالفعل');
      }

      // إنشاء المدير الجديد
      final newAdmin = AdminUser.create(
        username: username,
        email: email,
        password: password,
        fullName: fullName,
        role: role,
        permissions: permissions,
      );

      final success = await _databaseService.insertAdminUser(newAdmin);
      if (success) {
        await _databaseService.addAuditLog(
          adminId: _currentAdmin!.id,
          action: 'إنشاء مدير جديد',
          targetType: 'admin',
          targetId: newAdmin.id,
          details: 'تم إنشاء مدير جديد: $username',
        );

        return AuthResult.success(newAdmin);
      } else {
        return AuthResult.failure('فشل في إنشاء المدير');
      }
    } catch (e) {
      return AuthResult.failure('خطأ في إنشاء المدير: $e');
    }
  }

  /// حفظ الجلسة
  Future<void> _saveSession(AdminUser admin) async {
    final prefs = await SharedPreferences.getInstance();
    final sessionData = {
      'username': admin.username,
      'timestamp': DateTime.now().toIso8601String(),
    };
    await prefs.setString('admin_session', jsonEncode(sessionData));
  }

  /// مسح الجلسة
  Future<void> _clearSession() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('admin_session');
  }

  /// بدء مؤقت الجلسة
  void _startSessionTimer() {
    _sessionTimer?.cancel();
    _sessionTimer = Timer(_sessionTimeout, () {
      logout();
    });
  }

  /// تسجيل محاولة دخول فاشلة
  Future<void> _logFailedLogin(String username, String reason) async {
    await _databaseService.addAuditLog(
      adminId: 'system',
      action: 'محاولة دخول فاشلة',
      targetType: 'auth',
      details: 'اسم المستخدم: $username، السبب: $reason',
    );
  }

  /// تجديد الجلسة
  void refreshSession() {
    if (_currentAdmin != null) {
      _startSessionTimer();
    }
  }

  /// الحصول على جميع المدراء (للمدير العام فقط)
  Future<List<AdminUser>> getAllAdmins() async {
    if (!isSuperAdmin) {
      throw Exception('ليس لديك صلاحية لعرض المدراء');
    }
    return await _databaseService.getAllAdmins();
  }

  /// تعطيل مدير (للمدير العام فقط)
  Future<AuthResult> deactivateAdmin(String adminId) async {
    if (!isSuperAdmin) {
      return AuthResult.failure('ليس لديك صلاحية لتعطيل المدراء');
    }

    if (adminId == _currentAdmin!.id) {
      return AuthResult.failure('لا يمكنك تعطيل حسابك الخاص');
    }

    try {
      // هنا يمكن إضافة منطق تعطيل المدير في قاعدة البيانات
      await _databaseService.addAuditLog(
        adminId: _currentAdmin!.id,
        action: 'تعطيل مدير',
        targetType: 'admin',
        targetId: adminId,
        details: 'تم تعطيل المدير',
      );

      return AuthResult.success(_currentAdmin!);
    } catch (e) {
      return AuthResult.failure('خطأ في تعطيل المدير: $e');
    }
  }
}

/// نتيجة عملية المصادقة
class AuthResult {
  final bool isSuccess;
  final String? message;
  final AdminUser? admin;

  AuthResult._(this.isSuccess, this.message, this.admin);

  factory AuthResult.success(AdminUser admin) {
    return AuthResult._(true, null, admin);
  }

  factory AuthResult.failure(String message) {
    return AuthResult._(false, message, null);
  }
}

/// استثناءات المصادقة
class AuthException implements Exception {
  final String message;
  AuthException(this.message);

  @override
  String toString() => 'AuthException: $message';
}
