import 'package:flutter/material.dart';

import '../../core/services/auth_service.dart';
import '../models/admin_user.dart';

/// مقدم المصادقة
/// يدير حالة المصادقة في التطبيق
class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();

  AdminUser? _currentAdmin;
  bool _isLoading = false;
  String? _errorMessage;

  /// الحصول على المدير الحالي
  AdminUser? get currentAdmin => _currentAdmin;

  /// حالة التحميل
  bool get isLoading => _isLoading;

  /// رسالة الخطأ
  String? get errorMessage => _errorMessage;

  /// التحقق من تسجيل الدخول
  bool get isLoggedIn => _currentAdmin != null;

  /// التحقق من كون المستخدم مدير عام
  bool get isSuperAdmin => _currentAdmin?.role == AdminRole.superAdmin;

  /// تهيئة المقدم
  AuthProvider() {
    _checkSavedSession();
  }

  /// التحقق من الجلسة المحفوظة
  Future<void> _checkSavedSession() async {
    _setLoading(true);
    try {
      final hasSession = await _authService.checkSavedSession();
      if (hasSession) {
        _currentAdmin = _authService.currentAdmin;
      }
    } catch (e) {
      _setError('خطأ في استرجاع الجلسة: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// تسجيل الدخول
  Future<bool> login(String username, String password) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _authService.login(username, password);
      
      if (result.isSuccess) {
        _currentAdmin = result.admin;
        notifyListeners();
        return true;
      } else {
        _setError(result.message ?? 'فشل في تسجيل الدخول');
        return false;
      }
    } catch (e) {
      _setError('خطأ في تسجيل الدخول: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تسجيل الخروج
  Future<void> logout() async {
    _setLoading(true);
    try {
      await _authService.logout();
      _currentAdmin = null;
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تسجيل الخروج: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// التحقق من الصلاحية
  bool hasPermission(AdminPermission permission) {
    return _authService.hasPermission(permission);
  }

  /// التحقق من إمكانية الوصول لصفحة
  bool canAccessPage(String pageName) {
    return _authService.canAccessPage(pageName);
  }

  /// تغيير كلمة المرور
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    _setLoading(true);
    _clearError();

    // التحقق من تطابق كلمات المرور
    if (newPassword != confirmPassword) {
      _setError('كلمات المرور غير متطابقة');
      _setLoading(false);
      return false;
    }

    // التحقق من قوة كلمة المرور
    if (!_isPasswordStrong(newPassword)) {
      _setError('كلمة المرور ضعيفة. يجب أن تحتوي على 8 أحرف على الأقل مع أرقام وحروف');
      _setLoading(false);
      return false;
    }

    try {
      final result = await _authService.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
      );

      if (result.isSuccess) {
        return true;
      } else {
        _setError(result.message ?? 'فشل في تغيير كلمة المرور');
        return false;
      }
    } catch (e) {
      _setError('خطأ في تغيير كلمة المرور: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// إنشاء مدير جديد
  Future<bool> createAdmin({
    required String username,
    required String email,
    required String password,
    required String fullName,
    required AdminRole role,
    List<AdminPermission> permissions = const [],
  }) async {
    if (!isSuperAdmin) {
      _setError('ليس لديك صلاحية لإنشاء مدراء جدد');
      return false;
    }

    _setLoading(true);
    _clearError();

    // التحقق من صحة البيانات
    if (!_validateAdminData(username, email, password, fullName)) {
      _setLoading(false);
      return false;
    }

    try {
      final result = await _authService.createAdmin(
        username: username,
        email: email,
        password: password,
        fullName: fullName,
        role: role,
        permissions: permissions,
      );

      if (result.isSuccess) {
        return true;
      } else {
        _setError(result.message ?? 'فشل في إنشاء المدير');
        return false;
      }
    } catch (e) {
      _setError('خطأ في إنشاء المدير: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// الحصول على جميع المدراء
  Future<List<AdminUser>> getAllAdmins() async {
    if (!isSuperAdmin) {
      throw Exception('ليس لديك صلاحية لعرض المدراء');
    }

    try {
      return await _authService.getAllAdmins();
    } catch (e) {
      _setError('خطأ في الحصول على المدراء: $e');
      return [];
    }
  }

  /// تعطيل مدير
  Future<bool> deactivateAdmin(String adminId) async {
    if (!isSuperAdmin) {
      _setError('ليس لديك صلاحية لتعطيل المدراء');
      return false;
    }

    _setLoading(true);
    _clearError();

    try {
      final result = await _authService.deactivateAdmin(adminId);
      
      if (result.isSuccess) {
        return true;
      } else {
        _setError(result.message ?? 'فشل في تعطيل المدير');
        return false;
      }
    } catch (e) {
      _setError('خطأ في تعطيل المدير: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تجديد الجلسة
  void refreshSession() {
    _authService.refreshSession();
  }

  /// التحقق من صحة بيانات المدير
  bool _validateAdminData(String username, String email, String password, String fullName) {
    if (username.isEmpty || username.length < 3) {
      _setError('اسم المستخدم يجب أن يكون 3 أحرف على الأقل');
      return false;
    }

    if (!_isValidEmail(email)) {
      _setError('البريد الإلكتروني غير صحيح');
      return false;
    }

    if (!_isPasswordStrong(password)) {
      _setError('كلمة المرور ضعيفة. يجب أن تحتوي على 8 أحرف على الأقل مع أرقام وحروف');
      return false;
    }

    if (fullName.isEmpty || fullName.length < 2) {
      _setError('الاسم الكامل يجب أن يكون حرفين على الأقل');
      return false;
    }

    return true;
  }

  /// التحقق من صحة البريد الإلكتروني
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// التحقق من قوة كلمة المرور
  bool _isPasswordStrong(String password) {
    if (password.length < 8) return false;
    
    // يجب أن تحتوي على حرف واحد على الأقل
    if (!RegExp(r'[a-zA-Z]').hasMatch(password)) return false;
    
    // يجب أن تحتوي على رقم واحد على الأقل
    if (!RegExp(r'[0-9]').hasMatch(password)) return false;
    
    return true;
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// تعيين رسالة خطأ
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// مسح رسالة الخطأ
  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// مسح رسالة الخطأ يدوياً
  void clearError() {
    _clearError();
  }

  /// الحصول على اسم الدور المترجم
  String getRoleDisplayName(AdminRole role, bool isArabic) {
    return role.getLocalizedName(isArabic);
  }

  /// الحصول على اسم الصلاحية المترجم
  String getPermissionDisplayName(AdminPermission permission, bool isArabic) {
    return permission.getLocalizedName(isArabic);
  }

  /// التحقق من انتهاء الجلسة
  bool get isSessionExpired => !isLoggedIn && _currentAdmin == null;

  /// الحصول على معلومات المدير الحالي
  Map<String, dynamic> get currentAdminInfo {
    if (_currentAdmin == null) return {};
    
    return {
      'id': _currentAdmin!.id,
      'username': _currentAdmin!.username,
      'email': _currentAdmin!.email,
      'fullName': _currentAdmin!.fullName,
      'role': _currentAdmin!.role.arabicName,
      'permissions': _currentAdmin!.permissions.length,
      'lastLogin': _currentAdmin!.lastLoginAt,
    };
  }
}
