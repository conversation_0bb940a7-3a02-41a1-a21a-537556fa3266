import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/theme/app_colors.dart';
import '../../core/constants/app_constants.dart';
import '../../shared/providers/app_provider.dart';
import '../../shared/widgets/custom_app_bar.dart';
import '../../shared/widgets/category_card.dart';
import '../../shared/widgets/product_card.dart';
import '../../shared/widgets/banner_slider.dart';

/// الشاشة الرئيسية للتطبيق
/// متجر الملابس الرجالية المتطور
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        return Scaffold(
          backgroundColor: AppColors.background,

          // شريط التطبيق المخصص
          appBar: CustomAppBar(
            title: appProvider.getLocalizedText(
              AppConstants.appName,
              AppConstants.appNameEnglish,
            ),
            showCart: true,
            showSearch: true,
          ),

          // المحتوى الرئيسي
          body: RefreshIndicator(
            onRefresh: _onRefresh,
            child: SingleChildScrollView(
              controller: _scrollController,
              physics: const AlwaysScrollableScrollPhysics(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // شريط البانر المتحرك
                  const BannerSlider(),

                  const SizedBox(height: 20),

                  // قسم الأقسام
                  _buildCategoriesSection(appProvider),

                  const SizedBox(height: 20),

                  // قسم المنتجات المميزة
                  _buildFeaturedProductsSection(appProvider),

                  const SizedBox(height: 20),

                  // قسم المنتجات الجديدة
                  _buildNewProductsSection(appProvider),

                  const SizedBox(height: 20),

                  // قسم العروض والخصومات
                  _buildOffersSection(appProvider),

                  const SizedBox(height: 20),

                  // قسم الأكثر مبيعاً
                  _buildBestSellersSection(appProvider),

                  const SizedBox(height: 40),
                ],
              ),
            ),
          ),

          // شريط التنقل السفلي
          bottomNavigationBar: _buildBottomNavigationBar(appProvider),
        );
      },
    );
  }

  // تحديث الصفحة
  Future<void> _onRefresh() async {
    // هنا سيتم تحديث البيانات من الخادم
    await Future.delayed(const Duration(seconds: 2));
  }

  // بناء قسم الأقسام
  Widget _buildCategoriesSection(AppProvider appProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                appProvider.getLocalizedText('الأقسام', 'Categories'),
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () {
                  // الانتقال إلى صفحة جميع الأقسام
                },
                child: Text(
                  appProvider.getLocalizedText('عرض الكل', 'View All'),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: AppConstants.categories.length,
            itemBuilder: (context, index) {
              return CategoryCard(
                title: appProvider.getLocalizedText(
                  AppConstants.categories[index],
                  AppConstants.categoriesEnglish[index],
                ),
                icon: _getCategoryIcon(index),
                onTap: () {
                  // الانتقال إلى صفحة القسم
                },
              );
            },
          ),
        ),
      ],
    );
  }

  // بناء قسم المنتجات المميزة
  Widget _buildFeaturedProductsSection(AppProvider appProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                appProvider.getLocalizedText(
                  'منتجات مميزة',
                  'Featured Products',
                ),
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () {
                  // الانتقال إلى صفحة المنتجات المميزة
                },
                child: Text(
                  appProvider.getLocalizedText('عرض الكل', 'View All'),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 280,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: 5, // عدد المنتجات المميزة
            itemBuilder: (context, index) {
              return ProductCard(
                // هنا سيتم تمرير بيانات المنتج الحقيقية
                product: _getDummyProduct(index),
                onTap: () {
                  // الانتقال إلى صفحة تفاصيل المنتج
                },
              );
            },
          ),
        ),
      ],
    );
  }

  // بناء قسم المنتجات الجديدة
  Widget _buildNewProductsSection(AppProvider appProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                appProvider.getLocalizedText('وصل حديثاً', 'New Arrivals'),
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () {
                  // الانتقال إلى صفحة المنتجات الجديدة
                },
                child: Text(
                  appProvider.getLocalizedText('عرض الكل', 'View All'),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 280,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: 5,
            itemBuilder: (context, index) {
              return ProductCard(
                product: _getDummyProduct(index + 5),
                onTap: () {
                  // الانتقال إلى صفحة تفاصيل المنتج
                },
              );
            },
          ),
        ),
      ],
    );
  }

  // بناء قسم العروض
  Widget _buildOffersSection(AppProvider appProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            appProvider.getLocalizedText('عروض وخصومات', 'Offers & Discounts'),
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
        ),
        const SizedBox(height: 12),
        Container(
          height: 150,
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            gradient: AppColors.discountGradient,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  appProvider.getLocalizedText('خصم يصل إلى', 'Up to'),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Text(
                  '50%',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 48,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  appProvider.getLocalizedText(
                    'على مجموعة مختارة',
                    'On selected items',
                  ),
                  style: const TextStyle(color: Colors.white, fontSize: 16),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // بناء قسم الأكثر مبيعاً
  Widget _buildBestSellersSection(AppProvider appProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            appProvider.getLocalizedText('الأكثر مبيعاً', 'Best Sellers'),
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 280,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: 5,
            itemBuilder: (context, index) {
              return ProductCard(
                product: _getDummyProduct(index + 10),
                onTap: () {
                  // الانتقال إلى صفحة تفاصيل المنتج
                },
              );
            },
          ),
        ),
      ],
    );
  }

  // بناء شريط التنقل السفلي
  Widget _buildBottomNavigationBar(AppProvider appProvider) {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: 0,
      items: [
        BottomNavigationBarItem(
          icon: const Icon(Icons.home),
          label: appProvider.getLocalizedText('الرئيسية', 'Home'),
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.category),
          label: appProvider.getLocalizedText('الأقسام', 'Categories'),
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.shopping_cart),
          label: appProvider.getLocalizedText('السلة', 'Cart'),
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.favorite),
          label: appProvider.getLocalizedText('المفضلة', 'Wishlist'),
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.person),
          label: appProvider.getLocalizedText('الحساب', 'Account'),
        ),
      ],
    );
  }

  // الحصول على أيقونة القسم
  IconData _getCategoryIcon(int index) {
    const icons = [
      Icons.business_center, // الملابس الرسمية
      Icons.sports, // الملابس الرياضية
      Icons.checkroom, // الملابس الكاجوال
      Icons.ac_unit, // الملابس الشتوية
      Icons.wb_sunny, // الملابس الصيفية
      Icons.sports_soccer, // الأحذية
      Icons.watch, // الإكسسوارات
      Icons.mosque, // الملابس التقليدية
      Icons.local_offer, // العروض والخصومات
    ];
    return icons[index % icons.length];
  }

  // الحصول على منتج وهمي للاختبار
  Map<String, dynamic> _getDummyProduct(int index) {
    return {
      'id': 'product_$index',
      'name': 'منتج رقم ${index + 1}',
      'nameEnglish': 'Product ${index + 1}',
      'price': 150.0 + (index * 25),
      'originalPrice': 200.0 + (index * 25),
      'image': 'https://via.placeholder.com/300x300',
      'rating': 4.0 + (index % 2),
      'isOnSale': index % 3 == 0,
      'isNew': index % 4 == 0,
    };
  }
}
