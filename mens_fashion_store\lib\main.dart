import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'core/theme/app_theme.dart';
import 'core/constants/app_constants.dart';
import 'features/home/<USER>';
import 'shared/providers/app_provider.dart';
import 'shared/providers/auth_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة التخزين المحلي
  final prefs = await SharedPreferences.getInstance();

  runApp(MensFashionStoreApp(preferences: prefs));
}

class MensFashionStoreApp extends StatelessWidget {
  final SharedPreferences preferences;

  const MensFashionStoreApp({super.key, required this.preferences});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => AppProvider(preferences)),
        ChangeNotifierProvider(create: (context) => AuthProvider()),
      ],
      child: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          return MaterialApp(
            title: AppConstants.appName,
            debugShowCheckedModeBanner: false,

            // الثيم
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: _getThemeMode(appProvider.themeMode),

            // اللغة
            locale: Locale(appProvider.language),

            // الصفحة الرئيسية
            home: const HomeScreen(),

            // إعدادات إضافية
            builder: (context, child) {
              return Directionality(
                textDirection: appProvider.language == 'ar'
                    ? TextDirection.rtl
                    : TextDirection.ltr,
                child: child!,
              );
            },
          );
        },
      ),
    );
  }

  ThemeMode _getThemeMode(String themeMode) {
    switch (themeMode) {
      case 'dark':
        return ThemeMode.dark;
      case 'light':
        return ThemeMode.light;
      case 'system':
      default:
        return ThemeMode.system;
    }
  }
}
