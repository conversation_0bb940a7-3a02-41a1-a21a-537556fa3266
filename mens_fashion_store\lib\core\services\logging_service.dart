import 'package:logger/logger.dart';

/// خدمة التسجيل المركزية
/// تدير جميع عمليات تسجيل الأحداث والأخطاء في النظام
class LoggingService {
  static final LoggingService _instance = LoggingService._internal();
  factory LoggingService() => _instance;
  LoggingService._internal();

  late final Logger _logger;

  /// تهيئة خدمة التسجيل
  void initialize() {
    _logger = Logger(
      printer: PrettyPrinter(
        methodCount: 2,
        errorMethodCount: 8,
        lineLength: 120,
        colors: true,
        printEmojis: true,
      ),
      output: MultiOutput([
        ConsoleOutput(),
        // يمكن إضافة FileOutput هنا للحفظ في ملف
      ]),
    );
  }

  /// تسجيل معلومات عامة
  void info(String message, {String? tag, dynamic extra}) {
    _logger.i(_formatMessage(message, tag));
  }

  /// تسجيل تحذير
  void warning(String message, {String? tag, dynamic extra}) {
    _logger.w(_formatMessage(message, tag));
  }

  /// تسجيل خطأ
  void error(
    String message, {
    String? tag,
    dynamic error,
    StackTrace? stackTrace,
  }) {
    _logger.e(
      _formatMessage(message, tag),
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// تسجيل خطأ فادح
  void fatal(
    String message, {
    String? tag,
    dynamic error,
    StackTrace? stackTrace,
  }) {
    _logger.f(
      _formatMessage(message, tag),
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// تسجيل تصحيح الأخطاء
  void debug(String message, {String? tag, dynamic extra}) {
    _logger.d(_formatMessage(message, tag));
  }

  /// تسجيل تتبع
  void trace(String message, {String? tag, dynamic extra}) {
    _logger.t(_formatMessage(message, tag));
  }

  /// تنسيق الرسالة مع العلامة
  String _formatMessage(String message, String? tag) {
    if (tag != null) {
      return '[$tag] $message';
    }
    return message;
  }

  /// تسجيل عملية قاعدة البيانات
  void logDatabaseOperation({
    required String operation,
    required String table,
    String? details,
    bool success = true,
    dynamic error,
  }) {
    final message = success
        ? 'نجحت عملية $operation في جدول $table'
        : 'فشلت عملية $operation في جدول $table';

    if (success) {
      info(message, tag: 'DATABASE', extra: details);
    } else {
      error(message, tag: 'DATABASE', error: error);
    }
  }

  /// تسجيل عملية مصادقة
  void logAuthOperation({
    required String operation,
    required String username,
    bool success = true,
    String? reason,
    dynamic error,
  }) {
    final message = success
        ? 'نجحت عملية $operation للمستخدم $username'
        : 'فشلت عملية $operation للمستخدم $username${reason != null ? ': $reason' : ''}';

    if (success) {
      info(message, tag: 'AUTH');
    } else {
      warning(message, tag: 'AUTH', extra: error);
    }
  }

  /// تسجيل عملية إدارية
  void logAdminOperation({
    required String adminId,
    required String operation,
    required String targetType,
    String? targetId,
    String? details,
    bool success = true,
    dynamic error,
  }) {
    final message = success
        ? 'المدير $adminId نفذ عملية $operation على $targetType${targetId != null ? ' ($targetId)' : ''}'
        : 'فشل المدير $adminId في تنفيذ عملية $operation على $targetType${targetId != null ? ' ($targetId)' : ''}';

    if (success) {
      info(message, tag: 'ADMIN', extra: details);
    } else {
      error(message, tag: 'ADMIN', error: error);
    }
  }

  /// تسجيل خطأ في الشبكة
  void logNetworkError({
    required String endpoint,
    required String method,
    int? statusCode,
    String? errorMessage,
    dynamic error,
  }) {
    final message =
        'خطأ في الشبكة: $method $endpoint${statusCode != null ? ' (كود: $statusCode)' : ''}${errorMessage != null ? ' - $errorMessage' : ''}';
    error(message, tag: 'NETWORK', error: error);
  }

  /// تسجيل أداء العملية
  void logPerformance({
    required String operation,
    required Duration duration,
    Map<String, dynamic>? metadata,
  }) {
    final message = 'أداء العملية $operation: ${duration.inMilliseconds}ms';
    if (duration.inMilliseconds > 1000) {
      warning(message, tag: 'PERFORMANCE', extra: metadata);
    } else {
      debug(message, tag: 'PERFORMANCE', extra: metadata);
    }
  }

  /// تسجيل حدث أمني
  void logSecurityEvent({
    required String event,
    required String severity, // 'low', 'medium', 'high', 'critical'
    String? userId,
    String? ipAddress,
    Map<String, dynamic>? details,
  }) {
    final message =
        'حدث أمني: $event${userId != null ? ' (المستخدم: $userId)' : ''}${ipAddress != null ? ' (IP: $ipAddress)' : ''}';

    switch (severity.toLowerCase()) {
      case 'critical':
        fatal(message, tag: 'SECURITY');
        break;
      case 'high':
        error(message, tag: 'SECURITY');
        break;
      case 'medium':
        warning(message, tag: 'SECURITY');
        break;
      default:
        info(message, tag: 'SECURITY');
    }
  }

  /// تسجيل بداية جلسة المستخدم
  void logSessionStart(String userId, String userType) {
    info('بدء جلسة جديدة للمستخدم $userId (النوع: $userType)', tag: 'SESSION');
  }

  /// تسجيل انتهاء جلسة المستخدم
  void logSessionEnd(String userId, String reason) {
    info('انتهاء جلسة المستخدم $userId (السبب: $reason)', tag: 'SESSION');
  }

  /// تسجيل تغيير في البيانات الحساسة
  void logDataChange({
    required String adminId,
    required String dataType,
    required String action, // 'create', 'update', 'delete'
    String? recordId,
    Map<String, dynamic>? oldValues,
    Map<String, dynamic>? newValues,
  }) {
    final message =
        'المدير $adminId قام بـ $action في $dataType${recordId != null ? ' (المعرف: $recordId)' : ''}';
    info(
      message,
      tag: 'DATA_CHANGE',
      extra: {'oldValues': oldValues, 'newValues': newValues},
    );
  }

  /// تسجيل محاولة وصول غير مصرح بها
  void logUnauthorizedAccess({
    required String resource,
    String? userId,
    String? ipAddress,
    String? userAgent,
  }) {
    final message =
        'محاولة وصول غير مصرح بها إلى $resource${userId != null ? ' من المستخدم $userId' : ''}';
    logSecurityEvent(
      event: message,
      severity: 'high',
      userId: userId,
      ipAddress: ipAddress,
      details: {'userAgent': userAgent},
    );
  }

  /// تسجيل خطأ في التطبيق
  void logAppError({
    required String error,
    required String location,
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
  }) {
    final message = 'خطأ في التطبيق في $location: $error';
    this.error(
      message,
      tag: 'APP_ERROR',
      error: context,
      stackTrace: stackTrace,
    );
  }

  /// تسجيل تحديث النظام
  void logSystemUpdate({
    required String component,
    required String version,
    String? adminId,
    bool success = true,
    String? errorMessage,
  }) {
    final message = success
        ? 'تم تحديث $component إلى الإصدار $version${adminId != null ? ' بواسطة $adminId' : ''}'
        : 'فشل تحديث $component إلى الإصدار $version${errorMessage != null ? ': $errorMessage' : ''}';

    if (success) {
      info(message, tag: 'SYSTEM_UPDATE');
    } else {
      error(message, tag: 'SYSTEM_UPDATE');
    }
  }
}

/// مخرج وحدة التحكم المخصص
class ConsoleOutput extends LogOutput {
  @override
  void output(OutputEvent event) {
    for (var line in event.lines) {
      // في بيئة الإنتاج، يمكن إرسال السجلات إلى خدمة خارجية
      // ignore: avoid_print
      print(line);
    }
  }
}
