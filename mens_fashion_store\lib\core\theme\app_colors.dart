import 'package:flutter/material.dart';

/// ألوان التطبيق
/// متجر الملابس الرجالية المتطور
class AppColors {
  // الألوان الأساسية
  static const Color primary = Color(0xFF2196F3); // أزرق أنيق
  static const Color primaryLight = Color(0xFF64B5F6); // أزرق فاتح
  static const Color primaryDark = Color(0xFF1976D2); // أزرق داكن

  static const Color secondary = Color(0xFFD4AF37); // ذهبي فاخر
  static const Color secondaryLight = Color(0xFFE6C547); // ذهبي فاتح
  static const Color secondaryDark = Color(0xFFB8941F); // ذهبي داكن

  // ألوان الخلفية
  static const Color background = Color(0xFFFAFAFA); // أبيض مكسور
  static const Color surface = Color(0xFFFFFFFF); // أبيض نقي
  static const Color surfaceVariant = Color(0xFFF5F5F5); // رمادي فاتح جداً

  // ألوان النص
  static const Color textPrimary = Color(0xFF1A1A1A); // أسود للنص الأساسي
  static const Color textSecondary = Color(0xFF666666); // رمادي للنص الثانوي
  static const Color textLight = Color(0xFF999999); // رمادي فاتح للنص المساعد
  static const Color textOnPrimary = Color(
    0xFFFFFFFF,
  ); // أبيض على الخلفية الداكنة

  // ألوان الحالة
  static const Color success = Color(0xFF4CAF50); // أخضر للنجاح
  static const Color warning = Color(0xFFFF9800); // برتقالي للتحذير
  static const Color error = Color(0xFFF44336); // أحمر للخطأ
  static const Color info = Color(0xFF2196F3); // أزرق للمعلومات

  // ألوان إضافية للمتجر
  static const Color discount = Color(0xFFE91E63); // وردي للخصومات
  static const Color newProduct = Color(0xFF9C27B0); // بنفسجي للمنتجات الجديدة
  static const Color outOfStock = Color(
    0xFF757575,
  ); // رمادي للمنتجات غير المتوفرة
  static const Color inStock = Color(0xFF4CAF50); // أخضر للمنتجات المتوفرة

  // ألوان التقييم
  static const Color starFilled = Color(0xFFFFD700); // ذهبي للنجوم المملوءة
  static const Color starEmpty = Color(0xFFE0E0E0); // رمادي للنجوم الفارغة

  // ألوان الحدود
  static const Color border = Color(0xFFE0E0E0); // رمادي فاتح للحدود
  static const Color borderDark = Color(
    0xFFBDBDBD,
  ); // رمادي متوسط للحدود الداكنة
  static const Color divider = Color(0xFFEEEEEE); // رمادي فاتح جداً للفواصل

  // ألوان الظلال
  static const Color shadow = Color(0x1A000000); // ظل خفيف
  static const Color shadowMedium = Color(0x33000000); // ظل متوسط
  static const Color shadowDark = Color(0x4D000000); // ظل داكن

  // ألوان الأزرار
  static const Color buttonPrimary = primary;
  static const Color buttonSecondary = secondary;
  static const Color buttonDisabled = Color(0xFFBDBDBD);
  static const Color buttonText = textOnPrimary;

  // ألوان الإدخال
  static const Color inputFill = Color(0xFFF8F8F8);
  static const Color inputBorder = border;
  static const Color inputFocused = primary;
  static const Color inputError = error;

  // ألوان الشبكة (Grid)
  static const Color gridBackground = background;
  static const Color cardBackground = surface;
  static const Color cardShadow = shadow;

  // ألوان التنقل
  static const Color navigationBackground = surface;
  static const Color navigationSelected = primary;
  static const Color navigationUnselected = textLight;

  // ألوان الحالة للطلبات
  static const Color orderPending = Color(0xFFFF9800); // برتقالي - في الانتظار
  static const Color orderConfirmed = Color(0xFF2196F3); // أزرق - تم التأكيد
  static const Color orderProcessing = Color(
    0xFF9C27B0,
  ); // بنفسجي - قيد التحضير
  static const Color orderShipped = Color(0xFF607D8B); // رمادي مزرق - تم الشحن
  static const Color orderDelivered = Color(0xFF4CAF50); // أخضر - تم التسليم
  static const Color orderCancelled = Color(0xFFF44336); // أحمر - ملغي

  // تدرجات لونية
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, secondaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient discountGradient = LinearGradient(
    colors: [discount, Color(0xFFFF4081)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // ألوان شفافة
  static const Color overlay = Color(0x80000000); // تراكب شفاف
  static const Color overlayLight = Color(0x40000000); // تراكب خفيف
  static const Color overlayDark = Color(0xB3000000); // تراكب داكن

  // ألوان للوضع الليلي (Dark Mode)
  static const Color darkBackground = Color(0xFF121212);
  static const Color darkSurface = Color(0xFF1E1E1E);
  static const Color darkPrimary = Color(0xFFBB86FC);
  static const Color darkSecondary = Color(0xFF03DAC6);
  static const Color darkTextPrimary = Color(0xFFFFFFFF);
  static const Color darkTextSecondary = Color(0xFFBBBBBB);

  // دالة للحصول على لون حسب الحالة
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'في الانتظار':
      case 'pending':
        return orderPending;
      case 'تم التأكيد':
      case 'confirmed':
        return orderConfirmed;
      case 'قيد التحضير':
      case 'processing':
        return orderProcessing;
      case 'تم الشحن':
      case 'shipped':
        return orderShipped;
      case 'تم التسليم':
      case 'delivered':
        return orderDelivered;
      case 'ملغي':
      case 'cancelled':
        return orderCancelled;
      default:
        return textSecondary;
    }
  }

  // دالة للحصول على لون حسب التقييم
  static Color getRatingColor(double rating) {
    if (rating >= 4.5) return success;
    if (rating >= 3.5) return warning;
    if (rating >= 2.5) return const Color(0xFFFF5722);
    return error;
  }

  // دالة للحصول على لون حسب نسبة الخصم
  static Color getDiscountColor(double discountPercentage) {
    if (discountPercentage >= 50) return error;
    if (discountPercentage >= 30) return warning;
    if (discountPercentage >= 10) return info;
    return textSecondary;
  }
}
