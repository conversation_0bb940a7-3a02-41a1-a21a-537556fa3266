import 'dart:async';
import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';

import '../../shared/models/admin_user.dart';
import '../../shared/models/product.dart';

/// خدمة قاعدة البيانات المحلية
/// تدير جميع العمليات المتعلقة بقاعدة البيانات
class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  /// الحصول على قاعدة البيانات
  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  /// تهيئة قاعدة البيانات
  Future<Database> _initDatabase() async {
    final documentsDirectory = await getApplicationDocumentsDirectory();
    final path = join(documentsDirectory.path, 'mens_fashion_store.db');
    
    return await openDatabase(
      path,
      version: 1,
      onCreate: _createTables,
      onUpgrade: _upgradeDatabase,
    );
  }

  /// إنشاء الجداول
  Future<void> _createTables(Database db, int version) async {
    // جدول المدراء
    await db.execute('''
      CREATE TABLE admin_users (
        id TEXT PRIMARY KEY,
        username TEXT UNIQUE NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        full_name TEXT NOT NULL,
        role TEXT NOT NULL,
        permissions TEXT NOT NULL,
        created_at TEXT NOT NULL,
        last_login_at TEXT NOT NULL,
        is_active INTEGER NOT NULL DEFAULT 1,
        profile_image TEXT,
        settings TEXT
      )
    ''');

    // جدول المنتجات
    await db.execute('''
      CREATE TABLE products (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        name_english TEXT NOT NULL,
        description TEXT NOT NULL,
        description_english TEXT NOT NULL,
        price REAL NOT NULL,
        original_price REAL,
        category TEXT NOT NULL,
        subcategory TEXT,
        brand TEXT,
        images TEXT NOT NULL,
        sizes TEXT NOT NULL,
        colors TEXT NOT NULL,
        stock TEXT NOT NULL,
        rating REAL DEFAULT 0.0,
        review_count INTEGER DEFAULT 0,
        is_featured INTEGER DEFAULT 0,
        is_new INTEGER DEFAULT 0,
        is_on_sale INTEGER DEFAULT 0,
        discount_percentage REAL DEFAULT 0.0,
        tags TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        is_active INTEGER DEFAULT 1
      )
    ''');

    // جدول الطلبات
    await db.execute('''
      CREATE TABLE orders (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        items TEXT NOT NULL,
        total_amount REAL NOT NULL,
        status TEXT NOT NULL,
        shipping_address TEXT NOT NULL,
        payment_method TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // جدول المستخدمين العاديين
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        phone TEXT,
        addresses TEXT,
        created_at TEXT NOT NULL,
        last_active_at TEXT NOT NULL,
        is_active INTEGER DEFAULT 1
      )
    ''');

    // جدول سجل العمليات الإدارية
    await db.execute('''
      CREATE TABLE audit_logs (
        id TEXT PRIMARY KEY,
        admin_id TEXT NOT NULL,
        action TEXT NOT NULL,
        target_type TEXT NOT NULL,
        target_id TEXT,
        details TEXT,
        created_at TEXT NOT NULL,
        FOREIGN KEY (admin_id) REFERENCES admin_users (id)
      )
    ''');

    // جدول الأقسام
    await db.execute('''
      CREATE TABLE categories (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        name_english TEXT NOT NULL,
        description TEXT,
        icon TEXT,
        image TEXT,
        sort_order INTEGER DEFAULT 0,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL
      )
    ''');

    // إنشاء المدير الأساسي
    await _createSuperAdmin(db);
  }

  /// ترقية قاعدة البيانات
  Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion) async {
    // هنا يمكن إضافة منطق ترقية قاعدة البيانات في المستقبل
  }

  /// إنشاء المدير الأساسي
  Future<void> _createSuperAdmin(Database db) async {
    final superAdmin = AdminUser.createSuperAdmin(
      username: 'admin',
      email: '<EMAIL>',
      password: 'Admin@123456', // يجب تغييرها لاحقاً
      fullName: 'مدير المتجر الرئيسي',
    );

    await db.insert('admin_users', {
      'id': superAdmin.id,
      'username': superAdmin.username,
      'email': superAdmin.email,
      'password_hash': superAdmin.passwordHash,
      'full_name': superAdmin.fullName,
      'role': superAdmin.role.name,
      'permissions': superAdmin.permissions.map((p) => p.name).join(','),
      'created_at': superAdmin.createdAt.toIso8601String(),
      'last_login_at': superAdmin.lastLoginAt.toIso8601String(),
      'is_active': superAdmin.isActive ? 1 : 0,
      'profile_image': superAdmin.profileImage,
      'settings': '{}',
    });
  }

  // ==================== عمليات المدراء ====================

  /// إضافة مدير جديد
  Future<bool> insertAdminUser(AdminUser admin) async {
    try {
      final db = await database;
      await db.insert('admin_users', {
        'id': admin.id,
        'username': admin.username,
        'email': admin.email,
        'password_hash': admin.passwordHash,
        'full_name': admin.fullName,
        'role': admin.role.name,
        'permissions': admin.permissions.map((p) => p.name).join(','),
        'created_at': admin.createdAt.toIso8601String(),
        'last_login_at': admin.lastLoginAt.toIso8601String(),
        'is_active': admin.isActive ? 1 : 0,
        'profile_image': admin.profileImage,
        'settings': '{}',
      });
      return true;
    } catch (e) {
      print('خطأ في إضافة المدير: $e');
      return false;
    }
  }

  /// البحث عن مدير بالاسم المستخدم
  Future<AdminUser?> getAdminByUsername(String username) async {
    try {
      final db = await database;
      final result = await db.query(
        'admin_users',
        where: 'username = ? AND is_active = 1',
        whereArgs: [username],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return _adminFromMap(result.first);
      }
      return null;
    } catch (e) {
      print('خطأ في البحث عن المدير: $e');
      return null;
    }
  }

  /// البحث عن مدير بالبريد الإلكتروني
  Future<AdminUser?> getAdminByEmail(String email) async {
    try {
      final db = await database;
      final result = await db.query(
        'admin_users',
        where: 'email = ? AND is_active = 1',
        whereArgs: [email],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return _adminFromMap(result.first);
      }
      return null;
    } catch (e) {
      print('خطأ في البحث عن المدير: $e');
      return null;
    }
  }

  /// تحديث آخر تسجيل دخول للمدير
  Future<bool> updateAdminLastLogin(String adminId) async {
    try {
      final db = await database;
      await db.update(
        'admin_users',
        {'last_login_at': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [adminId],
      );
      return true;
    } catch (e) {
      print('خطأ في تحديث آخر تسجيل دخول: $e');
      return false;
    }
  }

  /// الحصول على جميع المدراء
  Future<List<AdminUser>> getAllAdmins() async {
    try {
      final db = await database;
      final result = await db.query('admin_users', orderBy: 'created_at DESC');
      return result.map((map) => _adminFromMap(map)).toList();
    } catch (e) {
      print('خطأ في الحصول على المدراء: $e');
      return [];
    }
  }

  // ==================== عمليات المنتجات ====================

  /// إضافة منتج جديد
  Future<bool> insertProduct(Product product) async {
    try {
      final db = await database;
      await db.insert('products', _productToMap(product));
      return true;
    } catch (e) {
      print('خطأ في إضافة المنتج: $e');
      return false;
    }
  }

  /// تحديث منتج
  Future<bool> updateProduct(Product product) async {
    try {
      final db = await database;
      await db.update(
        'products',
        _productToMap(product),
        where: 'id = ?',
        whereArgs: [product.id],
      );
      return true;
    } catch (e) {
      print('خطأ في تحديث المنتج: $e');
      return false;
    }
  }

  /// حذف منتج
  Future<bool> deleteProduct(String productId) async {
    try {
      final db = await database;
      await db.update(
        'products',
        {'is_active': 0},
        where: 'id = ?',
        whereArgs: [productId],
      );
      return true;
    } catch (e) {
      print('خطأ في حذف المنتج: $e');
      return false;
    }
  }

  /// الحصول على جميع المنتجات
  Future<List<Product>> getAllProducts() async {
    try {
      final db = await database;
      final result = await db.query(
        'products',
        where: 'is_active = 1',
        orderBy: 'created_at DESC',
      );
      return result.map((map) => _productFromMap(map)).toList();
    } catch (e) {
      print('خطأ في الحصول على المنتجات: $e');
      return [];
    }
  }

  // ==================== سجل العمليات ====================

  /// إضافة سجل عملية إدارية
  Future<void> addAuditLog({
    required String adminId,
    required String action,
    required String targetType,
    String? targetId,
    String? details,
  }) async {
    try {
      final db = await database;
      await db.insert('audit_logs', {
        'id': 'log_${DateTime.now().millisecondsSinceEpoch}',
        'admin_id': adminId,
        'action': action,
        'target_type': targetType,
        'target_id': targetId,
        'details': details,
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      print('خطأ في إضافة سجل العملية: $e');
    }
  }

  // ==================== دوال مساعدة ====================

  /// تحويل خريطة إلى مدير
  AdminUser _adminFromMap(Map<String, dynamic> map) {
    return AdminUser(
      id: map['id'],
      username: map['username'],
      email: map['email'],
      passwordHash: map['password_hash'],
      fullName: map['full_name'],
      role: AdminRole.values.firstWhere((r) => r.name == map['role']),
      permissions: (map['permissions'] as String)
          .split(',')
          .where((p) => p.isNotEmpty)
          .map((p) => AdminPermission.values.firstWhere((perm) => perm.name == p))
          .toList(),
      createdAt: DateTime.parse(map['created_at']),
      lastLoginAt: DateTime.parse(map['last_login_at']),
      isActive: map['is_active'] == 1,
      profileImage: map['profile_image'],
      settings: {},
    );
  }

  /// تحويل منتج إلى خريطة
  Map<String, dynamic> _productToMap(Product product) {
    return {
      'id': product.id,
      'name': product.name,
      'name_english': product.nameEnglish,
      'description': product.description,
      'description_english': product.descriptionEnglish,
      'price': product.price,
      'original_price': product.originalPrice,
      'category': product.category,
      'subcategory': product.subcategory,
      'brand': product.brand,
      'images': product.images.join(','),
      'sizes': product.sizes.join(','),
      'colors': product.colors.join(','),
      'stock': product.stock.entries.map((e) => '${e.key}:${e.value}').join(','),
      'rating': product.rating,
      'review_count': product.reviewCount,
      'is_featured': product.isFeatured ? 1 : 0,
      'is_new': product.isNew ? 1 : 0,
      'is_on_sale': product.isOnSale ? 1 : 0,
      'discount_percentage': product.discountPercentage,
      'tags': product.tags.join(','),
      'created_at': product.createdAt.toIso8601String(),
      'updated_at': product.updatedAt.toIso8601String(),
      'is_active': 1,
    };
  }

  /// تحويل خريطة إلى منتج
  Product _productFromMap(Map<String, dynamic> map) {
    return Product(
      id: map['id'],
      name: map['name'],
      nameEnglish: map['name_english'],
      description: map['description'],
      descriptionEnglish: map['description_english'],
      price: map['price'],
      originalPrice: map['original_price'],
      category: map['category'],
      subcategory: map['subcategory'],
      brand: map['brand'],
      images: (map['images'] as String).split(','),
      sizes: (map['sizes'] as String).split(','),
      colors: (map['colors'] as String).split(','),
      stock: Map.fromEntries(
        (map['stock'] as String).split(',').map((e) {
          final parts = e.split(':');
          return MapEntry(parts[0], int.parse(parts[1]));
        }),
      ),
      rating: map['rating'],
      reviewCount: map['review_count'],
      isFeatured: map['is_featured'] == 1,
      isNew: map['is_new'] == 1,
      isOnSale: map['is_on_sale'] == 1,
      discountPercentage: map['discount_percentage'],
      tags: (map['tags'] as String).split(','),
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  /// إغلاق قاعدة البيانات
  Future<void> close() async {
    final db = await database;
    await db.close();
  }
}
