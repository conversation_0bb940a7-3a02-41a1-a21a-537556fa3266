import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../core/theme/app_colors.dart';
import '../../../shared/providers/auth_provider.dart';
import '../../../shared/models/admin_user.dart';
import '../../../shared/widgets/custom_button.dart';
import '../auth/admin_login_screen.dart';

/// لوحة التحكم الإدارية الرئيسية
/// الواجهة المركزية لإدارة المتجر
class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // التحقق من تسجيل الدخول
        if (!authProvider.isLoggedIn) {
          return const AdminLoginScreen();
        }

        return Scaffold(
          backgroundColor: AppColors.background,
          appBar: _buildAppBar(authProvider),
          body: Row(
            children: [
              // الشريط الجانبي
              _buildSidebar(authProvider),

              // المحتوى الرئيسي
              Expanded(child: _buildMainContent()),
            ],
          ),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar(AuthProvider authProvider) {
    return AppBar(
      title: const Text(
        'لوحة التحكم الإدارية',
        style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
      ),
      backgroundColor: AppColors.primary,
      elevation: 0,
      automaticallyImplyLeading: false,
      actions: [
        // معلومات المدير
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              CircleAvatar(
                backgroundColor: AppColors.secondary,
                child: Text(
                  authProvider.currentAdmin?.fullName.substring(0, 1) ?? 'A',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    authProvider.currentAdmin?.fullName ?? '',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    authProvider.getRoleDisplayName(
                      authProvider.currentAdmin!.role,
                      true,
                    ),
                    style: const TextStyle(color: Colors.white70, fontSize: 12),
                  ),
                ],
              ),
              const SizedBox(width: 16),
              // زر تسجيل الخروج
              IconButton(
                icon: const Icon(Icons.logout, color: Colors.white),
                onPressed: () => _showLogoutDialog(authProvider),
                tooltip: 'تسجيل الخروج',
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSidebar(AuthProvider authProvider) {
    return Container(
      width: 250,
      color: AppColors.surface,
      child: Column(
        children: [
          // شعار التطبيق
          Container(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    gradient: AppColors.primaryGradient,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.store, color: Colors.white, size: 24),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'متجر الملابس الرجالية',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const Divider(),

          // قائمة التنقل
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(vertical: 8),
              children: [
                _buildSidebarItem(
                  icon: Icons.dashboard,
                  title: 'لوحة التحكم',
                  index: 0,
                  isSelected: _selectedIndex == 0,
                ),
                if (authProvider.hasPermission(AdminPermission.manageProducts))
                  _buildSidebarItem(
                    icon: Icons.inventory,
                    title: 'إدارة المنتجات',
                    index: 1,
                    isSelected: _selectedIndex == 1,
                  ),
                if (authProvider.hasPermission(AdminPermission.manageOrders))
                  _buildSidebarItem(
                    icon: Icons.shopping_cart,
                    title: 'إدارة الطلبات',
                    index: 2,
                    isSelected: _selectedIndex == 2,
                  ),
                if (authProvider.hasPermission(AdminPermission.manageUsers))
                  _buildSidebarItem(
                    icon: Icons.people,
                    title: 'إدارة المستخدمين',
                    index: 3,
                    isSelected: _selectedIndex == 3,
                  ),
                if (authProvider.hasPermission(AdminPermission.viewAnalytics))
                  _buildSidebarItem(
                    icon: Icons.analytics,
                    title: 'التقارير والإحصائيات',
                    index: 4,
                    isSelected: _selectedIndex == 4,
                  ),
                if (authProvider.hasPermission(
                  AdminPermission.manageCategories,
                ))
                  _buildSidebarItem(
                    icon: Icons.category,
                    title: 'إدارة الأقسام',
                    index: 5,
                    isSelected: _selectedIndex == 5,
                  ),
                if (authProvider.hasPermission(AdminPermission.manageSettings))
                  _buildSidebarItem(
                    icon: Icons.settings,
                    title: 'الإعدادات',
                    index: 6,
                    isSelected: _selectedIndex == 6,
                  ),
                if (authProvider.isSuperAdmin)
                  _buildSidebarItem(
                    icon: Icons.admin_panel_settings,
                    title: 'إدارة المدراء',
                    index: 7,
                    isSelected: _selectedIndex == 7,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSidebarItem({
    required IconData icon,
    required String title,
    required int index,
    required bool isSelected,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      child: ListTile(
        leading: Icon(
          icon,
          color: isSelected ? AppColors.primary : AppColors.textSecondary,
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isSelected ? AppColors.primary : AppColors.textPrimary,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
        selected: isSelected,
        selectedTileColor: AppColors.primary.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        onTap: () {
          setState(() {
            _selectedIndex = index;
          });
        },
      ),
    );
  }

  Widget _buildMainContent() {
    switch (_selectedIndex) {
      case 0:
        return _buildDashboardOverview();
      case 1:
        return _buildProductsManagement();
      case 2:
        return _buildOrdersManagement();
      case 3:
        return _buildUsersManagement();
      case 4:
        return _buildAnalytics();
      case 5:
        return _buildCategoriesManagement();
      case 6:
        return _buildSettings();
      case 7:
        return _buildAdminsManagement();
      default:
        return _buildDashboardOverview();
    }
  }

  Widget _buildDashboardOverview() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'نظرة عامة',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 24),

          // بطاقات الإحصائيات
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  title: 'إجمالي المنتجات',
                  value: '150',
                  icon: Icons.inventory,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  title: 'الطلبات الجديدة',
                  value: '25',
                  icon: Icons.shopping_cart,
                  color: AppColors.info,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  title: 'إجمالي المبيعات',
                  value: '15,750 ر.س',
                  icon: Icons.attach_money,
                  color: AppColors.success,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  title: 'المستخدمين النشطين',
                  value: '89',
                  icon: Icons.people,
                  color: AppColors.warning,
                ),
              ),
            ],
          ),

          const SizedBox(height: 32),

          // الإجراءات السريعة
          Text(
            'الإجراءات السريعة',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),

          Wrap(
            spacing: 16,
            runSpacing: 16,
            children: [
              CustomButton(
                text: 'إضافة منتج جديد',
                icon: Icons.add,
                onPressed: () {
                  setState(() {
                    _selectedIndex = 1;
                  });
                },
              ),
              CustomButton(
                text: 'عرض الطلبات',
                icon: Icons.list,
                isOutlined: true,
                onPressed: () {
                  setState(() {
                    _selectedIndex = 2;
                  });
                },
              ),
              CustomButton(
                text: 'التقارير',
                icon: Icons.analytics,
                isOutlined: true,
                onPressed: () {
                  setState(() {
                    _selectedIndex = 4;
                  });
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const Spacer(),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            value,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  // الصفحات الأخرى (مؤقتة)
  Widget _buildProductsManagement() {
    return const Center(
      child: Text(
        'إدارة المنتجات\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18),
      ),
    );
  }

  Widget _buildOrdersManagement() {
    return const Center(
      child: Text(
        'إدارة الطلبات\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18),
      ),
    );
  }

  Widget _buildUsersManagement() {
    return const Center(
      child: Text(
        'إدارة المستخدمين\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18),
      ),
    );
  }

  Widget _buildAnalytics() {
    return const Center(
      child: Text(
        'التقارير والإحصائيات\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18),
      ),
    );
  }

  Widget _buildCategoriesManagement() {
    return const Center(
      child: Text(
        'إدارة الأقسام\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18),
      ),
    );
  }

  Widget _buildSettings() {
    return const Center(
      child: Text(
        'الإعدادات\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18),
      ),
    );
  }

  Widget _buildAdminsManagement() {
    return const Center(
      child: Text(
        'إدارة المدراء\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18),
      ),
    );
  }

  void _showLogoutDialog(AuthProvider authProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          CustomButton(
            text: 'تسجيل الخروج',
            onPressed: () async {
              Navigator.of(context).pop();
              await authProvider.logout();
              if (mounted) {
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(
                    builder: (context) => const AdminLoginScreen(),
                  ),
                );
              }
            },
            backgroundColor: AppColors.error,
          ),
        ],
      ),
    );
  }
}
