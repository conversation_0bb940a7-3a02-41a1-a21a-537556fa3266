// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:mens_fashion_store/main.dart';

void main() {
  testWidgets('App starts correctly', (WidgetTester tester) async {
    // تهيئة SharedPreferences للاختبار
    SharedPreferences.setMockInitialValues({});
    final prefs = await SharedPreferences.getInstance();

    // بناء التطبيق
    await tester.pumpWidget(MensFashionStoreApp(preferences: prefs));

    // التحقق من وجود العنوان
    expect(find.text('متجر الملابس الرجالية'), findsOneWidget);
  });
}
