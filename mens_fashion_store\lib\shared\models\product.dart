/// نموذج المنتج
/// متجر الملابس الرجالية المتطور
class Product {
  final String id;
  final String name;
  final String nameEnglish;
  final String description;
  final String descriptionEnglish;
  final double price;
  final double? originalPrice; // السعر الأصلي قبل الخصم
  final String category;
  final String categoryEnglish;
  final List<String> images;
  final List<String> sizes;
  final List<String> colors;
  final Map<String, int> stock; // المخزون حسب الحجم واللون
  final double rating;
  final int reviewCount;
  final bool isNew;
  final bool isFeatured;
  final bool isOnSale;
  final double? discountPercentage;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? metadata; // بيانات إضافية

  Product({
    required this.id,
    required this.name,
    required this.nameEnglish,
    required this.description,
    required this.descriptionEnglish,
    required this.price,
    this.originalPrice,
    required this.category,
    required this.categoryEnglish,
    required this.images,
    required this.sizes,
    required this.colors,
    required this.stock,
    this.rating = 0.0,
    this.reviewCount = 0,
    this.isNew = false,
    this.isFeatured = false,
    this.isOnSale = false,
    this.discountPercentage,
    required this.createdAt,
    required this.updatedAt,
    this.metadata,
  });

  // تحويل من JSON
  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      nameEnglish: json['nameEnglish'] ?? '',
      description: json['description'] ?? '',
      descriptionEnglish: json['descriptionEnglish'] ?? '',
      price: (json['price'] ?? 0).toDouble(),
      originalPrice: json['originalPrice']?.toDouble(),
      category: json['category'] ?? '',
      categoryEnglish: json['categoryEnglish'] ?? '',
      images: List<String>.from(json['images'] ?? []),
      sizes: List<String>.from(json['sizes'] ?? []),
      colors: List<String>.from(json['colors'] ?? []),
      stock: Map<String, int>.from(json['stock'] ?? {}),
      rating: (json['rating'] ?? 0).toDouble(),
      reviewCount: json['reviewCount'] ?? 0,
      isNew: json['isNew'] ?? false,
      isFeatured: json['isFeatured'] ?? false,
      isOnSale: json['isOnSale'] ?? false,
      discountPercentage: json['discountPercentage']?.toDouble(),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
      metadata: json['metadata'],
    );
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'nameEnglish': nameEnglish,
      'description': description,
      'descriptionEnglish': descriptionEnglish,
      'price': price,
      'originalPrice': originalPrice,
      'category': category,
      'categoryEnglish': categoryEnglish,
      'images': images,
      'sizes': sizes,
      'colors': colors,
      'stock': stock,
      'rating': rating,
      'reviewCount': reviewCount,
      'isNew': isNew,
      'isFeatured': isFeatured,
      'isOnSale': isOnSale,
      'discountPercentage': discountPercentage,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'metadata': metadata,
    };
  }

  // نسخ مع تعديل
  Product copyWith({
    String? id,
    String? name,
    String? nameEnglish,
    String? description,
    String? descriptionEnglish,
    double? price,
    double? originalPrice,
    String? category,
    String? categoryEnglish,
    List<String>? images,
    List<String>? sizes,
    List<String>? colors,
    Map<String, int>? stock,
    double? rating,
    int? reviewCount,
    bool? isNew,
    bool? isFeatured,
    bool? isOnSale,
    double? discountPercentage,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      nameEnglish: nameEnglish ?? this.nameEnglish,
      description: description ?? this.description,
      descriptionEnglish: descriptionEnglish ?? this.descriptionEnglish,
      price: price ?? this.price,
      originalPrice: originalPrice ?? this.originalPrice,
      category: category ?? this.category,
      categoryEnglish: categoryEnglish ?? this.categoryEnglish,
      images: images ?? this.images,
      sizes: sizes ?? this.sizes,
      colors: colors ?? this.colors,
      stock: stock ?? this.stock,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      isNew: isNew ?? this.isNew,
      isFeatured: isFeatured ?? this.isFeatured,
      isOnSale: isOnSale ?? this.isOnSale,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  // الحصول على الصورة الرئيسية
  String get mainImage => images.isNotEmpty ? images.first : '';

  // التحقق من توفر المنتج
  bool get isAvailable => getTotalStock() > 0;

  // الحصول على إجمالي المخزون
  int getTotalStock() {
    return stock.values.fold(0, (sum, quantity) => sum + quantity);
  }

  // الحصول على المخزون لحجم ولون معين
  int getStockForVariant(String size, String color) {
    final key = '${size}_$color';
    return stock[key] ?? 0;
  }

  // التحقق من توفر حجم ولون معين
  bool isVariantAvailable(String size, String color) {
    return getStockForVariant(size, color) > 0;
  }

  // الحصول على السعر النهائي بعد الخصم
  double get finalPrice {
    if (isOnSale && discountPercentage != null) {
      return price * (1 - discountPercentage! / 100);
    }
    return price;
  }

  // الحصول على مبلغ الخصم
  double get discountAmount {
    if (isOnSale && originalPrice != null) {
      return originalPrice! - finalPrice;
    }
    return 0.0;
  }

  // التحقق من كون المنتج جديد (أقل من 30 يوم)
  bool get isRecentlyAdded {
    final now = DateTime.now();
    final difference = now.difference(createdAt).inDays;
    return difference <= 30;
  }

  // الحصول على تقييم المنتج كنص
  String get ratingText {
    if (reviewCount == 0) return 'لا توجد تقييمات';
    return '${rating.toStringAsFixed(1)} (${reviewCount} تقييم)';
  }

  // التحقق من المساواة
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Product && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Product(id: $id, name: $name, price: $price, category: $category)';
  }
}
