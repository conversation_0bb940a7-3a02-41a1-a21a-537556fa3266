1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mensfashion.store.mens_fashion_store"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Desktop\flutter_application_1\mens_fashion_store\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->C:\Users\<USER>\Desktop\flutter_application_1\mens_fashion_store\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->C:\Users\<USER>\Desktop\flutter_application_1\mens_fashion_store\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->C:\Users\<USER>\Desktop\flutter_application_1\mens_fashion_store\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->C:\Users\<USER>\Desktop\flutter_application_1\mens_fashion_store\android\app\src\main\AndroidManifest.xml:41:13-72
25-->C:\Users\<USER>\Desktop\flutter_application_1\mens_fashion_store\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->C:\Users\<USER>\Desktop\flutter_application_1\mens_fashion_store\android\app\src\main\AndroidManifest.xml:42:13-50
27-->C:\Users\<USER>\Desktop\flutter_application_1\mens_fashion_store\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29    </queries>
30
31    <permission
31-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
32        android:name="com.mensfashion.store.mens_fashion_store.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
32-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
33        android:protectionLevel="signature" />
33-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
34
35    <uses-permission android:name="com.mensfashion.store.mens_fashion_store.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
36
37    <application
38        android:name="android.app.Application"
39        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
40        android:debuggable="true"
41        android:extractNativeLibs="true"
42        android:icon="@mipmap/ic_launcher"
43        android:label="mens_fashion_store" >
44        <activity
45            android:name="com.mensfashion.store.mens_fashion_store.MainActivity"
46            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
47            android:exported="true"
48            android:hardwareAccelerated="true"
49            android:launchMode="singleTop"
50            android:taskAffinity=""
51            android:theme="@style/LaunchTheme"
52            android:windowSoftInputMode="adjustResize" >
53
54            <!--
55                 Specifies an Android theme to apply to this Activity as soon as
56                 the Android process has started. This theme is visible to the user
57                 while the Flutter UI initializes. After that, this theme continues
58                 to determine the Window background behind the Flutter UI.
59            -->
60            <meta-data
61                android:name="io.flutter.embedding.android.NormalTheme"
62                android:resource="@style/NormalTheme" />
63
64            <intent-filter>
65                <action android:name="android.intent.action.MAIN" />
66
67                <category android:name="android.intent.category.LAUNCHER" />
68            </intent-filter>
69        </activity>
70        <!--
71             Don't delete the meta-data below.
72             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
73        -->
74        <meta-data
75            android:name="flutterEmbedding"
76            android:value="2" />
77
78        <activity
78-->[:url_launcher_android] C:\Users\<USER>\Desktop\flutter_application_1\mens_fashion_store\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
79            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
79-->[:url_launcher_android] C:\Users\<USER>\Desktop\flutter_application_1\mens_fashion_store\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
80            android:exported="false"
80-->[:url_launcher_android] C:\Users\<USER>\Desktop\flutter_application_1\mens_fashion_store\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
81            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
81-->[:url_launcher_android] C:\Users\<USER>\Desktop\flutter_application_1\mens_fashion_store\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
82
83        <uses-library
83-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
84            android:name="androidx.window.extensions"
84-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
85            android:required="false" />
85-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
86        <uses-library
86-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
87            android:name="androidx.window.sidecar"
87-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
88            android:required="false" />
88-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
89
90        <provider
90-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
91            android:name="androidx.startup.InitializationProvider"
91-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
92            android:authorities="com.mensfashion.store.mens_fashion_store.androidx-startup"
92-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
93            android:exported="false" >
93-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
94            <meta-data
94-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
95                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
95-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
96                android:value="androidx.startup" />
96-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
97            <meta-data
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
98                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
99                android:value="androidx.startup" />
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
100        </provider>
101
102        <receiver
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
103            android:name="androidx.profileinstaller.ProfileInstallReceiver"
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
104            android:directBootAware="false"
104-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
105            android:enabled="true"
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
106            android:exported="true"
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
107            android:permission="android.permission.DUMP" >
107-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
108            <intent-filter>
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
109                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
110            </intent-filter>
111            <intent-filter>
111-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
112                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
113            </intent-filter>
114            <intent-filter>
114-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
115                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
115-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
115-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
116            </intent-filter>
117            <intent-filter>
117-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
118                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
119            </intent-filter>
120        </receiver>
121    </application>
122
123</manifest>
