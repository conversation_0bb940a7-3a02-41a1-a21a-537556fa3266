/// ثوابت التطبيق الأساسية
/// متجر VIP للملابس الرجالية الفاخرة
class AppConstants {
  // معلومات التطبيق
  static const String appName = 'VIP';
  static const String appNameEnglish = 'VIP';
  static const String appVersion = '1.0.0';
  static const String appDescription =
      'متجر VIP للملابس الرجالية الفاخرة والعصرية';

  // أقسام المتجر
  static const List<String> categories = [
    'الملابس الرسمية',
    'الملابس الرياضية',
    'الملابس الكاجوال',
    'الملابس الشتوية',
    'الملابس الصيفية',
    'الأحذية',
    'الإكسسوارات',
    'الملابس التقليدية',
    'الساعات والمجوهرات',
    'العروض والخصومات',
  ];

  static const List<String> categoriesEnglish = [
    'Formal Wear',
    'Sportswear',
    'Casual Wear',
    'Winter Wear',
    'Summer Wear',
    'Shoes',
    'Accessories',
    'Traditional Wear',
    'Watches & Jewelry',
    'Offers & Discounts',
  ];

  // أحجام الملابس
  static const List<String> clothingSizes = [
    'XS',
    'S',
    'M',
    'L',
    'XL',
    'XXL',
    'XXXL',
  ];

  // أحجام الأحذية
  static const List<String> shoeSizes = [
    '38',
    '39',
    '40',
    '41',
    '42',
    '43',
    '44',
    '45',
    '46',
    '47',
  ];

  // ألوان المنتجات
  static const List<String> productColors = [
    'أسود',
    'أبيض',
    'رمادي',
    'أزرق',
    'بني',
    'أخضر',
    'أحمر',
    'بيج',
    'كحلي',
  ];

  // حالات الطلب
  static const List<String> orderStatuses = [
    'في الانتظار',
    'تم التأكيد',
    'قيد التحضير',
    'تم الشحن',
    'تم التسليم',
    'ملغي',
  ];

  // طرق الدفع
  static const List<String> paymentMethods = [
    'الدفع عند الاستلام',
    'بطاقة ائتمان',
    'محفظة رقمية',
    'تحويل بنكي',
  ];

  // إعدادات التطبيق
  static const int maxCartItems = 50;
  static const int maxWishlistItems = 100;
  static const double minOrderAmount = 50.0;
  static const double freeShippingThreshold = 200.0;
  static const double defaultShippingCost = 25.0;

  // مدة انتهاء الجلسة (بالدقائق)
  static const int sessionTimeoutMinutes = 30;

  // حد أقصى لحجم الصورة (بالميجابايت)
  static const int maxImageSizeMB = 5;

  // عدد المنتجات في الصفحة الواحدة
  static const int productsPerPage = 20;

  // رسائل الخطأ الافتراضية
  static const String defaultErrorMessage =
      'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
  static const String networkErrorMessage =
      'تحقق من اتصال الإنترنت وحاول مرة أخرى.';
  static const String serverErrorMessage =
      'خطأ في الخادم. يرجى المحاولة لاحقاً.';

  // روابط التواصل الاجتماعي
  static const String facebookUrl = 'https://facebook.com/vipstore';
  static const String instagramUrl = 'https://instagram.com/vipstore';
  static const String twitterUrl = 'https://twitter.com/vipstore';
  static const String whatsappNumber = '+966501234567';

  // معلومات الاتصال
  static const String supportEmail = '<EMAIL>';
  static const String supportPhone = '+966501234567';
  static const String companyAddress = 'الرياض، المملكة العربية السعودية';

  // إعدادات الكاش
  static const Duration cacheExpiration = Duration(hours: 24);
  static const int maxCacheSize = 100; // عدد العناصر

  // مفاتيح التخزين المحلي
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String cartDataKey = 'cart_data';
  static const String wishlistDataKey = 'wishlist_data';
  static const String settingsKey = 'app_settings';
  static const String languageKey = 'app_language';
  static const String themeKey = 'app_theme';

  // إعدادات الإشعارات
  static const String fcmTopicAllUsers = 'all_users';
  static const String fcmTopicOffers = 'offers';
  static const String fcmTopicNewProducts = 'new_products';

  // تقييمات المنتجات
  static const double minRating = 1.0;
  static const double maxRating = 5.0;
  static const int maxReviewLength = 500;

  // إعدادات البحث
  static const int minSearchLength = 2;
  static const int maxSearchHistory = 10;
  static const int searchResultsLimit = 50;

  // أنماط العرض
  static const String gridViewType = 'grid';
  static const String listViewType = 'list';

  // فترات التحديث التلقائي
  static const Duration autoRefreshInterval = Duration(minutes: 5);
  static const Duration cartSyncInterval = Duration(seconds: 30);

  // حدود الخصومات
  static const double maxDiscountPercentage = 70.0;
  static const double minDiscountAmount = 10.0;

  // إعدادات الصور
  static const double imageQuality = 0.8;
  static const int thumbnailSize = 300;
  static const int fullImageSize = 1200;

  // أولويات الإشعارات
  static const String highPriority = 'high';
  static const String normalPriority = 'normal';
  static const String lowPriority = 'low';
}
