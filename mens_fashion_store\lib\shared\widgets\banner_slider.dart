import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../core/theme/app_colors.dart';

/// شريط البانر المتحرك
/// متجر الملابس الرجالية المتطور
class BannerSlider extends StatefulWidget {
  final List<BannerItem>? banners;
  final double height;
  final Duration autoPlayDuration;
  final bool autoPlay;

  const BannerSlider({
    super.key,
    this.banners,
    this.height = 200,
    this.autoPlayDuration = const Duration(seconds: 5),
    this.autoPlay = true,
  });

  @override
  State<BannerSlider> createState() => _BannerSliderState();
}

class _BannerSliderState extends State<BannerSlider> {
  late PageController _pageController;
  int _currentIndex = 0;
  late List<BannerItem> _banners;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _banners = widget.banners ?? _getDefaultBanners();

    if (widget.autoPlay && _banners.isNotEmpty) {
      _startAutoPlay();
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _startAutoPlay() {
    Future.delayed(widget.autoPlayDuration, () {
      if (mounted && _banners.isNotEmpty) {
        final nextIndex = (_currentIndex + 1) % _banners.length;
        _pageController.animateToPage(
          nextIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        _startAutoPlay();
      }
    });
  }

  List<BannerItem> _getDefaultBanners() {
    return [
      BannerItem(
        title: 'مجموعة الخريف الجديدة',
        subtitle: 'خصم يصل إلى 40%',
        imageUrl:
            'https://via.placeholder.com/400x200/1A1A1A/FFFFFF?text=Banner+1',
        backgroundColor: AppColors.primary,
      ),
      BannerItem(
        title: 'ملابس رياضية عصرية',
        subtitle: 'اكتشف التشكيلة الجديدة',
        imageUrl:
            'https://via.placeholder.com/400x200/D4AF37/FFFFFF?text=Banner+2',
        backgroundColor: AppColors.secondary,
      ),
      BannerItem(
        title: 'عروض نهاية الموسم',
        subtitle: 'خصومات تصل إلى 60%',
        imageUrl:
            'https://via.placeholder.com/400x200/E91E63/FFFFFF?text=Banner+3',
        backgroundColor: AppColors.discount,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    if (_banners.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      height: widget.height,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Stack(
        children: [
          // البانرات
          PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            itemCount: _banners.length,
            itemBuilder: (context, index) {
              return _buildBannerItem(_banners[index]);
            },
          ),

          // مؤشرات الصفحات
          if (_banners.length > 1)
            Positioned(
              bottom: 16,
              left: 0,
              right: 0,
              child: _buildPageIndicators(),
            ),
        ],
      ),
    );
  }

  Widget _buildBannerItem(BannerItem banner) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: LinearGradient(
          colors: [
            banner.backgroundColor.withValues(alpha: 0.8),
            banner.backgroundColor,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            // صورة الخلفية
            if (banner.imageUrl.isNotEmpty)
              Positioned.fill(
                child: CachedNetworkImage(
                  imageUrl: banner.imageUrl,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    color: banner.backgroundColor.withValues(alpha: 0.3),
                  ),
                  errorWidget: (context, url, error) => Container(
                    color: banner.backgroundColor.withValues(alpha: 0.3),
                  ),
                ),
              ),

            // تراكب شفاف
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.black.withValues(alpha: 0.3),
                      Colors.transparent,
                    ],
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                  ),
                ),
              ),
            ),

            // النص
            Positioned(
              bottom: 20,
              left: 20,
              right: 20,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (banner.title.isNotEmpty)
                    Text(
                      banner.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        shadows: [
                          Shadow(
                            offset: Offset(0, 1),
                            blurRadius: 3,
                            color: Colors.black26,
                          ),
                        ],
                      ),
                    ),
                  if (banner.subtitle.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      banner.subtitle,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        shadows: [
                          Shadow(
                            offset: Offset(0, 1),
                            blurRadius: 3,
                            color: Colors.black26,
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPageIndicators() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(_banners.length, (index) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          width: _currentIndex == index ? 24 : 8,
          height: 8,
          decoration: BoxDecoration(
            color: _currentIndex == index
                ? Colors.white
                : Colors.white.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(4),
          ),
        );
      }),
    );
  }
}

/// عنصر البانر
class BannerItem {
  final String title;
  final String subtitle;
  final String imageUrl;
  final Color backgroundColor;
  final VoidCallback? onTap;

  BannerItem({
    required this.title,
    required this.subtitle,
    required this.imageUrl,
    required this.backgroundColor,
    this.onTap,
  });
}
